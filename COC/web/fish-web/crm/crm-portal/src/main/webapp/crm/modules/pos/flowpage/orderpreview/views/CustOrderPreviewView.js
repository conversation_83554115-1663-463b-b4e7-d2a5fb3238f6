define([
    'hbs!crm/modules/pos/flowpage/orderpreview/templates/CustOrderPreviewTpl.hbs',
    'i18n!crm/modules/pos/workspace/i18n/OrderEntry',
    'crm/modules/fbf/bfm/utils/Controller',
    'crm/modules/customer/actions/OrderAction',
    'crm/modules/pos/common/constant/AttrCodeDef',
    'crm/modules/pos/common/utils/BoUtils',
    'crm/modules/pos/common/constant/ServTypeDef',
    'crm/modules/pos/common/constant/BindTypeDef',
    'crm/modules/pos/webflow/constants/PageDef',
    'crm/modules/pos/common/constant/IntroDef',
    'crm/modules/common/constant/OrderTypeDef',
    'crm/modules/pos/common/constant/SubsEventDef',
    'crm/modules/pos/flowpage/custorderInfo/actions/CustOrderInfoAction',
    'crm/modules/common/util/CommonUtils',
    'crm/modules/common/actions/ConfigItemAction',
    'css!crm/modules/pos/flowpage/orderpreview/css/OrderPreview.css'
], function (
    template,
    I18N,
    Controller,
    OrderAction,
    AttrCodeDef,
    BoUtils,
    ServTypeDef,
    BindTypeDef,
    PageDef,
    IntroDef,
    OrderTypeDef,
    SubsEventDef,
    GenderAction,
    CommonUtils,
    ConfigItemAction
) {
    return portal.BaseView.extend({
        template: template,
        events: {
            'click .js-slide-toggle-order': 'onSlideToggle'
        },
        initialize: function (options) {
            this.options = options || {};
            this.flowData = options.flowData || {};
            this.custOrder = options.custOrder;
            this.orderItem = options.orderItem;
            this.showPreAccNbr = options.showPreAccNbr;
            this.initDate = null;
            this.supportSaveFlag = 'Y';
            this.commonDataId = options.flowData.commonDataId;
            this.cust = this.custOrder.CUST;
            this.orderViewMap = {};
            this.validateObj = {};
            this.genderArray = [];
            if (fish.isArray(this.custOrder.ORDER_ITEM)) {
                this.orderItemList = this.custOrder.ORDER_ITEM;
            } else {
                this.orderItemList = [this.custOrder.ORDER_ITEM];
            }

            this.$installInfoViewArr = [];
            // 初始化导航条数据
            this.navOrderItemList = [];
            this.initNavOrderList();
            this.serialize = fish.extend(
                {
                    itemList: fish.nest(this.navOrderItemList, 'id', 'parentId', 'itemList'),
                    navOrderItemList: this.navOrderItemList
                },
                I18N
            );
            this.trigger('showBack', true);
            Controller.getInstance().registerCommonDataId(this.flowData.commonDataId, this);
            this.fixInstallOrderViewList = [];
            this.hybirdSellMode = ConfigItemAction.qryParamValue('CUSTOMER_CARE', 'PROMO_COUPON', 'HYBRID_SALE_MODE');
        },

        afterRender: function () {
            // 初始化属性
            this.$navBar = this.$('#navbar');
            this.$scrollspy = this.$('.oe-order-preview');
            this.initGenderDetail();
            this.$orderItemsContainer = this.$('.js-order-item-container');
            this.$installOrderContainer = this.$('.js-install-order-container');
            this.initCustOrderBasicInfo();
            this.initOrderDetail();
            this.initOrderFee();
            this.initCustOrderAttchment();
            this.initNavBar();
            this.triggerEvent();
            this.initOrderItemList();

            if (portal.appGlobal.get('isBolLogin') && fish.store.get('accountOpeningMethod') == 'boundleOffer') {
                $('.order-content .order-fixed-btn-list').css({ position: 'absolute', width: '100%' });
                $('.pos-order-btn-list').css({ position: 'absolute', width: '100%' });
                var tips = '';
                if (fish.store.get('accountOpeningMethod') == 'boundleOffer') {
                    tips = IntroDef.BOUNDLE_OFFER_STEP17;
                }
                setTimeout(function () {
                    fish.trigger(IntroDef.ADD_INTRO, {
                        element: '#next-btn-intro',
                        tipsContents: tips,
                        position: 'top'
                    });
                }, 500);
            }

            this.initDelivery();
            this.initPromoView();
            this.initCouponInfo();
            this.initBonusInfo();
            this.initInstallOrder();
            this.initSlideToggle();
        },

        initSlideToggle: function () {
            // 初始化时候默认小三角折叠。
            if ($('.js-slide-toggle-order') && $('.js-slide-toggle-order').length > 0) {
                fish.forEach($('.js-slide-toggle-order'), function (item) {
                    $(item).toggleClass('icon-rotate-270');
                    $(item).parent().find('.list-box').slideUp();
                });
            }
        },

        // 内层的lists-box伸缩
        onSlideToggle: function (event) {
            // 所有的窗口保持向上状态。
            if ($('.js-slide-toggle-order') && $('.js-slide-toggle-order').length > 0) {
                fish.forEach($('.js-slide-toggle-order'), function (item) {
                    if (item != event.target) {
                        if (!$(item).hasClass('icon-rotate-270')) {
                            $(item).toggleClass('icon-rotate-270');
                        }
                        $(item).parent().find('.list-box').slideUp();
                    }
                });
            }

            // 当前窗口保持展开状态
            $this = $(event.target);
            $this.toggleClass('icon-rotate-270');
            $this.parent().find('.list-box').slideToggle();
        },

        initInstallOrder: function () {
            this.installOrderArray = this.custOrder.FIX_INSTALL_ORDER;

            if (this.orderItemList[0].ORDER_TYPE != OrderTypeDef.DEMAND_ORDER && this.installOrderArray && !fish.isEmpty(this.installOrderArray)) {
                this.$installOrderContainer.show();

                fish.each(
                    this.installOrderArray,
                    function (fixInstallOrder) {
                        var installOrderId = fixInstallOrder && fixInstallOrder.INSTALL_ORDER_ID,
                            key = 'CUST_ORDER_' + this.custOrder.CUST_ORDER_ID + '_INSTALL_ORDER_' + installOrderId;

                        var parentOrderItem = fish.find(this.orderItemList, function (orderItem) {
                            var installOrderId = BoUtils.getOrderItemAttrValue(orderItem, AttrCodeDef.EXP_FIX_INSTALL_ORDER_ID);
                            return fixInstallOrder.INSTALL_ORDER_ID == installOrderId;
                        });

                        this.$installOrderContainer.append('<div id="' + key + '" style="margin-bottom: 12px"></div>');

                        this.requireView({
                            url: 'crm/modules/fix/InstallInfo/views/InstallInfoPanelView',
                            selector: '#' + key,
                            viewOption: {
                                fixInstallOrder: fixInstallOrder,
                                orderItem: parentOrderItem,
                                custOrder: this.custOrder
                            },
                            callback: function (viewInstance) {
                                this.fixInstallOrderViewList.push(viewInstance);
                            }
                        });
                    }.bind(this)
                );
            } else {
                this.$('.li-delivery-install-preview').hide();
                this.$installOrderContainer.hide();
            }

            // this.installOrderArray = this.custOrder.FIX_INSTALL_ORDER;
            // if (this.installOrderArray && !fish.isEmpty(this.installOrderArray)) {
            //     if (this.installOrderArray.length == 1){
            //         this.$(".js-installInfo-title").hide();
            //     }
            //     fish.each(this.installOrderArray, function (fixInstallOrder, index) {
            //         var installOrderId = fixInstallOrder && fixInstallOrder.INSTALL_ORDER_ID,
            //             key = "CUST_ORDER_" + this.custOrder.CUST_ORDER_ID + "_INSTALL_ORDER_" + installOrderId;
            //         var  classKey = "js-"+key;
            //         this.$installOrderContainer.append('<div id="' + key + '" class= "'+classKey+'"></div>');
            //         var parentOrderItem = fish.find(this.orderItemList, function (orderItem) {
            //             var installOrderId = BoUtils.getOrderItemAttrValue(orderItem, AttrCodeDef.EXP_FIX_INSTALL_ORDER_ID);
            //             return fixInstallOrder.INSTALL_ORDER_ID == installOrderId;
            //         });
            //         if (!!parentOrderItem) {
            //             this.requireView({
            //                 url: 'crm/modules/fix/InstallInfo/views/popwin/BundleInstallInfoPopView',
            //                 selector: '.'+classKey,
            //                 viewOption: {
            //                     orderItem: parentOrderItem,
            //                     fixInstallOrder: fixInstallOrder,
            //                     custOrder: this.custOrder,
            //                 },
            //                 callback: function ($view) {
            //                     this.$installInfoViewArr.push($view);
            //                     this.listenTo($view, 'synchronizeAppointmentInformation', function (installOrderId) {
            //                         var view = fish.find(this.$installInfoViewArr, function (item) {
            //                             return item.installOrderId == installOrderId;
            //                         });
            //                         if (!fish.isEmpty(view)) {
            //                             view.synchronizeAppointmentInformation();
            //                         }
            //                     }.bind(this));
            //                 }.bind(this)
            //             });
            //         }
            //     }.bind(this));
            // } else {
            //     this.$(".li-delivery-install-preview").hide();
            //     this.$installOrderContainer.hide();
            // }
        },

        initGenderDetail: function () {
            GenderAction.qryGenderListSyns(
                function (data) {
                    if (data && data.dataValueList && data.dataValueList.length > 0) {
                        this.genderArray = data.dataValueList;
                    }
                }.bind(this)
            );
        },

        /**
         * 初始化客户订单基本信息组件
         */
        initCustOrderBasicInfo: function () {
            this.requireView({
                url: 'crm/standardizedcomponents/pos/components/CustOrderBasicInfoView',
                selector: '.js-cust-order-basic-info',
                viewOption: {
                    custOrder: this.custOrder,
                    cust: this.cust,
                    genderArray: this.genderArray,
                    commonDataId: this.commonDataId
                },
                callback: function ($view) {
                    this.custOrderBasicInfoView = $view;
                    this.setupCustOrderBasicInfoEventListeners($view);
                }.bind(this)
            });
        },

        /**
         * 设置客户订单基本信息组件事件监听器
         */
        setupCustOrderBasicInfoEventListeners: function ($componentView) {
            // 监听数据加载完成事件
            $componentView.on('dataLoaded', function (eventData) {
                console.log('客户订单基本信息加载完成:', eventData);
            });

            // 监听错误事件
            $componentView.on('error', function (eventData) {
                console.error('客户订单基本信息组件错误:', eventData);
                fish.error(eventData.data.message || '客户订单基本信息加载失败');
            });
        },

        initNavOrderItemList: function (bundleOperOrder, orderItemIdArr, bundleOperOrderList) {
            fish.each(
                bundleOperOrder.BUNDLE_MEMBER_OPER_ORDER,
                function (bundleMemberOperOrder) {
                    if (bundleMemberOperOrder.ORDER_ITEM_ID) {
                        this.navOrderItemList.push({
                            id: bundleMemberOperOrder.ORDER_ITEM_ID,
                            name: bundleMemberOperOrder.SUBS_PLAN_NAME,
                            icon: 'iconfont pto-arr-arrowr-ight pr10',
                            parentId: bundleOperOrder.ORDER_ITEM_ID
                        });
                        orderItemIdArr.push(bundleMemberOperOrder.ORDER_ITEM_ID);
                    }
                    // 特殊处理一下VOIP/DN
                    if (bundleMemberOperOrder.SERV_TYPE == ServTypeDef.FIX_VOIP) {
                        var orderItem4DNList = this.getDNOrderItemList(bundleMemberOperOrder.SUBS_ID) || [];

                        var id = bundleMemberOperOrder.ORDER_ITEM_ID;
                        if (!bundleMemberOperOrder.ORDER_ITEM_ID) {
                            id = bundleMemberOperOrder.SUBS_ID;
                            this.navOrderItemList.push({
                                id: id,
                                name: bundleMemberOperOrder.SUBS_PLAN_NAME,
                                icon: 'iconfont pto-arr-arrowr-ight pr10',
                                parentId: bundleOperOrder.ORDER_ITEM_ID
                            });
                        }

                        fish.each(
                            orderItem4DNList,
                            function (orderItem) {
                                this.navOrderItemList.push({
                                    id: orderItem.ORDER_ITEM_ID,
                                    name: orderItem.SUBS_PLAN_NAME,
                                    icon: 'iconfont pto-arr-arrowr-ight pr10',
                                    parentId: id
                                });
                                orderItemIdArr.push(orderItem.ORDER_ITEM_ID);
                            }.bind(this)
                        );
                    }

                    var bundleOperOrderItem = fish.findWhere(bundleOperOrderList, { ORDER_ITEM_ID: bundleMemberOperOrder.ORDER_ITEM_ID });
                    if (bundleOperOrderItem) {
                        this.initNavOrderItemList(bundleOperOrderItem, orderItemIdArr, bundleOperOrderList);
                    }
                }.bind(this)
            );
        },
        /**
         * 左侧导航条
         */
        initNavOrderList: function () {
            // bundle订单
            var orderItemIdArr = [];
            var bundleOperOrderList = this.custOrder.BUNDLE_OPER_ORDER;
            fish.each(
                bundleOperOrderList,
                function (bundleOperOrder) {
                    if (!bundleOperOrder.PARENT_ORDER_ITEM_ID) {
                        // Bundle订单
                        this.navOrderItemList.push({
                            id: bundleOperOrder.ORDER_ITEM_ID,
                            name: bundleOperOrder.SUBS_PLAN_NAME || this.orderItem.SUBS_PLAN_NAME,
                            icon: 'iconfont pto-arr-arrowr-ight pr10'
                        });
                        orderItemIdArr.push(bundleOperOrder.ORDER_ITEM_ID);

                        this.initNavOrderItemList(bundleOperOrder, orderItemIdArr, bundleOperOrderList);
                        // // 安装信息
                        // this.navOrderItemList.push({
                        //     id: bundleOperOrder.ORDER_ITEM_ID + "-install-info",
                        //     name: I18N.ORDER_ENTRY_INSTALL_INFO,
                        //     icon: "iconfont icon-action-management pr10",
                        //     parentId: bundleOperOrder.ORDER_ITEM_ID
                        // });

                        // bundle成员订单
                    }
                }.bind(this)
            );

            var orderItemList = fish.filter(
                this.orderItemList,
                function (orderItem) {
                    return !fish.contains(orderItemIdArr, orderItem.ORDER_ITEM_ID);
                }.bind(this)
            );

            fish.each(
                orderItemList,
                function (orderItem) {
                    this.navOrderItemList.push({
                        id: orderItem.ORDER_ITEM_ID,
                        name: orderItem.SUBS_PLAN_NAME,
                        icon: 'iconfont pto-arr-arrowr-ight pr10'
                    });
                }.bind(this)
            );

            // 排序
            this.orderItemList = fish.sortBy(this.orderItemList, function (orderItem) {
                var currIndex = 9999;
                $.each(orderItemIdArr, function (orderItemId, index) {
                    if (orderItem.ORDER_ITEM_ID == orderItemId) {
                        currIndex = index;
                    }
                });
                return currIndex;
            });

            // 如果不是Bundle相关的业务,那么this.navOrderItemList长度和this.orderItemList长度相等
            // 如果是Bundle相关的业务,通过上面的逻辑处理后,可能长度会不相等
            // 在这里去重,去完重之后再比较是否相等,如果还不相等,则以this.orderItemList长度为准
            // 先单独将this.navOrderItemList取出重复数据一次，解决开户保存草稿单重复数据继续去重失效的问题  taskid:#11150765 同时防止保留数据没有name，增加name补全
            if (this.orderItemList.length != this.navOrderItemList.length) {
                let uniqueItems = {};
                let result = [];

                this.navOrderItemList.forEach((item) => {
                    let id = item.id.toString();

                    if (!uniqueItems[id]) {
                        uniqueItems[id] = item;
                    } else {
                        if (uniqueItems[id].parentId === undefined && item.parentId !== undefined) {
                            uniqueItems[id] = item;
                        } else if (uniqueItems[id].parentId === undefined && item.parentId === undefined) {
                            // 如果name为空，用去除数据的name填充
                            if (!uniqueItems[id].name && item.name) {
                                uniqueItems[id].name = item.name;
                            }
                        }
                    }
                });

                for (let key in uniqueItems) {
                    result.push(uniqueItems[key]);
                }

                // 将map的值转换为数组并赋值给this.navOrderItemList
                this.navOrderItemList = result;
            }

            if (this.orderItemList.length != this.navOrderItemList.length) {
                // 去重
                var newNavOrderItemArr = [];
                var idArr = [];
                fish.each(
                    this.navOrderItemList,
                    function (navOrderItem) {
                        if (!fish.contains(idArr, navOrderItem.id)) {
                            idArr.push(navOrderItem.id);
                            newNavOrderItemArr.push(navOrderItem);
                        }
                    }.bind(this)
                );
                // 去完重之后,再比较长度是否相等
                // 如果不相等,再剔除newNavOrderItemArr中不存在于this.orderItemList中的orderItem
                if (this.orderItemList.length != newNavOrderItemArr.length) {
                    var orderItemIdList = this.orderItemList.map(function (item) {
                        return item.ORDER_ITEM_ID;
                    });
                    var tmpNavOrderItemArr = newNavOrderItemArr;
                    fish.each(tmpNavOrderItemArr, function (tmpNavOrderItem, index) {
                        if (tmpNavOrderItem && !fish.contains(orderItemIdList, tmpNavOrderItem.id)) {
                            newNavOrderItemArr.splice(index, 1);
                        }
                    });
                }
                // 剔除完之后赋值给this.navOrderItemList
                this.navOrderItemList = newNavOrderItemArr;
            }

            // 新开户不需要重新将principle的放在第一位
            if (SubsEventDef.NEW_CONNECTION != this.custOrder.SUBS_EVENT_ID) {
                // principle的放在第一位
                var principleOrderItem = null;
                var index = 1;
                for (i = 0; i < this.navOrderItemList.length; i++) {
                    var parentId = this.navOrderItemList[i].parentId;
                    var id = this.navOrderItemList[i].id;
                    if (parentId) {
                        var primaryFlag = this.getIsPrimary(id, parentId);
                        if (primaryFlag && primaryFlag == 'Y') {
                            principleOrderItem = this.navOrderItemList[i];
                            index = i;
                        }
                    }
                }
                if (principleOrderItem && index != 1) {
                    var memOrderItem = this.navOrderItemList[1];
                    this.navOrderItemList[1] = principleOrderItem;
                    this.navOrderItemList[index] = memOrderItem;
                }
            }
        },

        getIsPrimary: function (orderItemId, bundleOrderItemId) {
            var bundleOperOrder = fish.find(this.custOrder.BUNDLE_OPER_ORDER, function (temp) {
                return temp.ORDER_ITEM_ID == bundleOrderItemId;
            });
            var bundleMemberOperOrder = fish.find(bundleOperOrder.BUNDLE_MEMBER_OPER_ORDER, function (item) {
                return item.ORDER_ITEM_ID == orderItemId;
            });
            return bundleMemberOperOrder ? bundleMemberOperOrder.IS_PRIMARY : null;
        },

        /**
         * 获取DN
         */
        getDNOrderItemList: function (parentSubsId) {
            return fish.filter(this.orderItemList, function (orderItem) {
                var subsBindOrder = fish.find(orderItem.SUBS_BIND_ORDER || [], function (item) {
                    return item.SUBS_ID == parentSubsId && item.BIND_TYPE == BindTypeDef.PARENT_CHILD_RELATION;
                });
                return !!subsBindOrder;
            });
        },

        triggerEvent: function () {
            this.trigger('isPreviewWindow');
        },

        initNavBar: function () {
            var that = this;

            // 是否点击标题的标记，如果锚点点击仍然无法滚动到这里，导致高亮显示标题非点击标题，需要标记来特殊处理
            that.clickFlag = false;

            this.$scrollspy.scrollspy({
                target: '#navbar',
                offset: 20
            });
            this.$scrollspy.on('scroll', function () {
                if (that.clickFlag) {
                    that.clickFlag = false;
                } else {
                    that.$('.bs-docs-sidenav li span').removeClass('oe-selected');
                    that.$('.active').find('span').first().addClass('oe-selected');
                }
            });
            this.$navBar.affix();
            // this.$(".bs-docs-sidenav").children('li').removeClass("active");
            // this.$(".bs-docs-sidenav").children('li:first').find("span").addClass("oe-selected");

            this.$('.bs-docs-sidenav li span').first().addClass('oe-selected');
            // 平滑移动
            this.$('li>a[href]').click(function () {
                that.clickFlag = true; // 表示点击事件进来，不触发上面滚动监听的操作
                $('.bs-docs-sidenav li span').removeClass('oe-selected');
                $(this).parent().find('span').first().addClass('oe-selected');
                // that.$scrollspy.animate({
                //     scrollTop: that.$($(this).attr('href')).offset().top
                // }, 500);
                return true;
            });
        },

        initInstallInfo: function (postFixInstallOrder, orderItem) {
            // this.$(".js-install-info-link").show();
            // this.$(".js-install-info").show();
            this.options.oper = 'view';
            this.requireView({
                url: 'crm/post/modules/order/flowpage/products/fix/views/popwin/BundleInstallInfoPopView',
                selector: '.js-order-item-detail-' + orderItem.ORDER_ITEM_ID + '-install-info',
                // selector: "#install-info",
                viewOption: {
                    postFixInstallOrder: postFixInstallOrder,
                    custOrder: this.custOrder,
                    orderItem: orderItem
                },
                callback: function ($view) {
                    this.$installInfoViewArr.push($view);
                }.bind(this)
            });

            this.$('.js-order-item-detail').panel({
                // collapsible: true,
            });
        },

        initPromoView: function () {
            if (this.hybirdSellMode !== 'Y') {
                return;
            }
            var option = { custOrder: this.custOrder, commonDataId: this.commonDataId };
            this.requireView({
                url: 'crm/modules/order/promo/views/PromoPreviewView',
                selector: '.js-promoPreviewDiv',
                viewOption: option,
                callback: function ($view) {
                    this.$promoInfoView = $view;
                    if (!$view.$promoInfoView && !$view.$promoInfoView) {
                        this.$('.li-promo-info-preview').hide();
                    }
                }.bind(this)
            });
        },

        initDelivery: function () {
            var option = { custOrder: this.custOrder, commonDataId: this.commonDataId };
            this.requireView({
                url: 'crm/modules/fix/deliveryinfo/views/DeliveryInfoView',
                selector: '.js-deliveryInfoPreviewDiv',
                viewOption: option,
                callback: function ($view) {
                    this.$deliveryView = $view;

                    if (!$view.$deliveryView && !$view.$deliveryGoodsView) {
                        this.$('.li-delivery-info-preview').hide();
                    }
                }.bind(this)
            });
        },

        initCouponInfo: function () {
            if (this.hybirdSellMode !== 'Y') {
                this.$('.li-coupon-info-preview').hide();
                return;
            }
            var option = { custOrder: this.custOrder, commonDataId: this.commonDataId };
            this.requireView({
                url: 'crm/modules/pos/flowpage/common/views/GoodsCouponInfoView',
                selector: '.js-couponInfoPreviewDiv',
                viewOption: option,
                callback: function ($view) {
                    this.$couponInfoView = $view;
                    this.listenTo(
                        this.$couponInfoView,
                        'refreshBonusPreviewView',
                        function () {
                            if (this.$bonusInfoView) {
                                this.$bonusInfoView.refreshData();
                            }
                        }.bind(this)
                    );
                }.bind(this)
            });
        },

        initBonusInfo: function () {
            if (this.hybirdSellMode !== 'Y') {
                this.$('.li-bonus-info-preview').hide();
                return;
            }
            var option = { custOrder: this.custOrder, commonDataId: this.commonDataId };
            this.requireView({
                url: 'crm/modules/order/bonus/views/BonusPreviewView',
                selector: '.js-bonusInfoPreviewDiv',
                viewOption: option,
                callback: function ($view) {
                    this.$bonusInfoView = $view;
                }.bind(this)
            });
        },

        initCustOrderAttchment: function () {
            this.$('.js-cust-order-attachment-detail').panel({
                // collapsible: true,
            });

            var contactlessFlag = false;
            var contactlessAttrId = AttrUtil.getAttrIdByCode('EXP_CONTACTLESS_FLAG');
            var attrObj = fish.find(
                this.custOrder.CUST_ORDER_ATTR,
                function (custOrderAttr) {
                    return custOrderAttr.ATTR_ID == contactlessAttrId;
                }.bind(this)
            );
            if (attrObj && attrObj.VALUE == 'Y') {
                contactlessFlag = true;
            }

            this.requireView({
                url: 'crm/modules/pos/flowpage/business/component/views/CustOrderAttachmentView',
                selector: '.js-cust-order-attachment',
                viewOption: {
                    flowFlag: true,
                    commonDataId: this.commonDataId,
                    custOrderId: this.custOrder.CUST_ORDER_ID,
                    custId: this.custOrder.CUST_ID,
                    orderItemList: this.orderItemList,
                    contactlessFlag: contactlessFlag
                },
                callback: function ($view) {
                    this.$attachmentView = $view;
                }.bind(this)
            });
            // test print view
            // this.requireView({
            //     url: 'crm/modules/order/print/views/PrintFilesView',
            //     selector: '.js-cust-order-attachment',
            //     viewOption: {
            //         flowFlag: true,
            //         custOrder: this.custOrder,
            //         orderItemList: this.orderItemList
            //     },
            //     callback: function ($view) {
            //         this.$attachmentView = $view;
            //     }.bind(this)
            // });
        },

        /**
         * 初始化订单详情（仅处理订单项相关逻辑）
         */
        initOrderDetail: function () {
            // bundle订单
            var orderItemIdArr = [];
            fish.each(
                this.custOrder.BUNDLE_OPER_ORDER,
                function (bundleOperOrder) {
                    // Bundle订单
                    orderItemIdArr.push(bundleOperOrder.ORDER_ITEM_ID);
                    this.initOrderItemDetail(bundleOperOrder.ORDER_ITEM_ID);

                    // var orderItem4Bundle = fish.find(this.orderItemList, function (orderItem) {
                    //     return orderItem.ORDER_ITEM_ID == bundleOperOrder.ORDER_ITEM_ID;
                    // });

                    // // 安装信息
                    // var installOrderId = BoUtils.getOrderItemAttrValue(orderItem4Bundle, AttrCodeDef.POST_FIX_INSTALL_ORDER_ID);
                    // if (installOrderId) {
                    //     var postFixInstallOrder = fish.find(this.custOrder.POST_FIX_INSTALL_ORDER, function (item) {
                    //         return item.INSTALL_ORDER_ID == installOrderId;
                    //     });
                    //     this.initInstallInfo(postFixInstallOrder, orderItem4Bundle);
                    // }

                    // bundle成员订单
                    fish.each(
                        bundleOperOrder.BUNDLE_MEMBER_OPER_ORDER,
                        function (bundleMemberOperOrder) {
                            orderItemIdArr.push(bundleMemberOperOrder.ORDER_ITEM_ID);
                            this.initOrderItemDetail(bundleMemberOperOrder.ORDER_ITEM_ID);
                        }.bind(this)
                    );
                }.bind(this)
            );

            var orderItemList = fish.filter(
                this.orderItemList,
                function (orderItem) {
                    return !fish.contains(orderItemIdArr, orderItem.ORDER_ITEM_ID);
                }.bind(this)
            );

            fish.each(
                orderItemList,
                function (orderItem) {
                    this.renderOrderItem(orderItem, '.js-order-item-detail-' + orderItem.ORDER_ITEM_ID);
                }.bind(this)
            );

            this.$('.js-order-item-detail').panel({
                //  collapsible: true,
            });
        },

        /**
         * 按照订单项生成订单详情
         *
         * @param orderItemId
         */
        initOrderItemDetail: function (orderItemId) {
            fish.each(
                this.orderItemList,
                function (orderItem) {
                    if (orderItemId == orderItem.ORDER_ITEM_ID) {
                        this.renderOrderItem(orderItem, '.js-order-item-detail-' + orderItem.ORDER_ITEM_ID);
                    }
                }.bind(this)
            );
        },

        // initOrderItemList: function () {
        //     // 循环加载OrderItem列表
        //     if (this.orderItemList && !fish.isEmpty(this.orderItemList)) {
        //         this.$('.js-order-item-nav').append('<ul class="nav js-order-item-ul"></ul>')
        //
        //         fish.each(this.orderItemList, function (orderItem, index) {
        //             var orderItemId = orderItem && orderItem.ORDER_ITEM_ID,
        //                 key = "CUST_ORDER_" + this.custOrder.CUST_ORDER_ID + "_ORDER_ITEM_" + orderItem.ORDER_ITEM_ID;
        //             this.$orderItemsContainer.append('<div id="' + key + '"></div>')
        //             // 3343669
        //             if (!orderItem.SUBS_PLAN_NAME) {
        //                 orderItem.SUBS_PLAN_NAME = orderItem.OFFER_NAME;
        //             }
        //             this.requireView({
        //                 url: 'crm/modules/pos/flowpage/orderpreview/views/OrderItemListPreviewView',
        //                 selector: "#" + key,
        //                 viewOption: {
        //                     orderItemDetail: orderItem,
        //                     orderItemId: orderItemId,
        //                     orderItemDivId: key,
        //                     showPreAccNbr: this.showPreAccNbr,
        //                     showFlag: this.orderItemId
        //                 },
        //                 callback: function ($view) {
        //                     if (this.orderItemView === 'Y') {
        //                         this.orderItemId == orderItem.ORDER_ITEM_ID && $view.expandPanel();
        //                     } else {
        //                         index == 0 && $view.expandPanel();
        //                     }
        //
        //                 }.bind(this)
        //             });
        //         }.bind(this));
        //     } else {
        //         this.$orderItemsContainer.hide();
        //     }
        // },

        initOrderItemList: function () {
            // 循环加载OrderItem列表
            if (this.orderItemList && !fish.isEmpty(this.orderItemList)) {
                // this.$('.js-order-item-nav').append('<ul class="nav js-order-item-ul"></ul>')

                fish.each(
                    this.orderItemList,
                    function (orderItem, index) {
                        var orderItemId = orderItem && orderItem.ORDER_ITEM_ID,
                            key = orderItemId;
                        this.$orderItemsContainer.append('<div id="' + key + '" style="margin-bottom: 12px"></div>');
                        // 3343669
                        if (!orderItem.SUBS_PLAN_NAME) {
                            orderItem.SUBS_PLAN_NAME = orderItem.OFFER_NAME;
                        }
                        this.requireView({
                            url: 'crm/modules/pos/flowpage/orderpreview/views/OrderItemListPreviewView',
                            selector: '#' + key,
                            viewOption: {
                                orderItemDetail: orderItem,
                                orderItemId: orderItemId,
                                orderItemDivId: key,
                                showPreAccNbr: this.showPreAccNbr,
                                showFlag: this.orderItemId,
                                buildNavFlag: 'N'
                            },
                            callback: function ($view) {
                                if (this.orderItemView === 'Y') {
                                    this.orderItemId == orderItem.ORDER_ITEM_ID && $view.expandPanel();
                                } else {
                                    index == 0 && $view.expandPanel();
                                }
                            }.bind(this)
                        });
                    }.bind(this)
                );
            } else {
                this.$orderItemsContainer.hide();
            }
        },

        /**
         * 初始化订单费用
         */
        initOrderFee: function () {
            this.$('.js-order-fee-info-preview').panel({
                collapsible: true
            });
            this.requireView({
                url: 'crm/modules/pos/flowpage/orderpreview/views/OrderFeeInfoPreviewView',
                selector: '.js-order-fee-info-preview-detail',
                viewOption: this.options,
                callback: function ($view) {
                    this.$orderFeeInfoPreviewView = $view;
                    this.listenTo(
                        $view,
                        'canShowContractPreview',
                        function () {
                            this.trigger('showContractPreview', true);
                        }.bind(this)
                    );
                    // 重新试算
                    fish.on(
                        'preview:feePreview',
                        function (x) {
                            if (!CommonUtils.isSubView(this, x)) {
                                return;
                            }
                            if ($view.feePreview) {
                                $view.feePreview(true);
                            }
                        }.bind(this)
                    );
                }.bind(this)
            });
        },

        /**
         * 渲染订单详情
         */
        renderOrderItem: function (orderItem, selctor) {
            var url = PageDef.getPreviewPage(null, orderItem.SUBS_EVENT_ID, orderItem.SERV_TYPE);
            if (!url) {
                return;
            }
            var initData = {
                orderItem: orderItem,
                flowData: this.flowData,
                showPreAccNbr: this.showPreAccNbr,
                custOrder: this.custOrder
            };

            require([url], function (View) {
                var $viewInstance = new View(initData);
                // 注册页面
                $viewInstance.once(
                    'beforeRender',
                    function () {
                        Controller.getInstance().registerCommonDataId(this.flowData.commonDataId, $viewInstance);
                        Controller.getInstance().registerOrderItem(orderItem.ORDER_ITEM_ID, $viewInstance);
                    }.bind(this)
                );
                // 刷新数据
                $viewInstance.once(
                    'afterRender',
                    function () {
                        this.orderViewMap[orderItem.ORDER_ITEM_ID] = $viewInstance;
                    }.bind(this)
                );

                // 渲染页面
                this.setView(selctor, $viewInstance, false);
                this.renderViews([$viewInstance]);
            }.bind(this));
        },

        validateData: function () {
            var validateResult = null;
            if (this.$installInfoViewArr) {
                fish.each(this.$installInfoViewArr, function ($view) {
                    var validateObj = $view.validateData();
                    if (validateObj && !validateObj.validateFlag) {
                        validateResult = validateObj;
                    }
                });
            }
            if (this.$deliveryView) {
                var validateObj = this.$deliveryView.validateData();
                if (validateObj && !validateObj.validateFlag) {
                    validateResult = validateObj;
                }
            }
            // 附件校验
            if (this.$attachmentView) {
                var validateObj = this.$attachmentView.validateData();
                if (validateObj && !validateObj.validateFlag) {
                    validateResult = validateObj;
                }
            }
            if (this.fixInstallOrderViewList) {
                fish.each(this.fixInstallOrderViewList, function ($view) {
                    var validateObj = $view.appointmentValidateData();
                    if (validateObj && !validateObj.validateFlag) {
                        validateResult = validateObj;
                    }
                });
            }
            if (this.$bonusInfoView) {
                var validateObj = this.$bonusInfoView.validateData();
                if (validateObj && !validateObj.validateFlag) {
                    validateResult = validateObj;
                }
            }
            var unsubscribePromptMsg = null;
            var param = {};
            param.custOrder = this.custOrder;
            OrderAction.checkUnsubscribePrompt(
                param,
                function (result) {
                    if (result.promptMsgList) {
                        unsubscribePromptMsg = result.promptMsgList;
                    } else if (result.promptMsgInfo) {
                        unsubscribePromptMsg = [result.promptMsgInfo];
                    }
                }.bind(this)
            );

            if (validateResult) {
                return validateResult;
            } else {
                if (unsubscribePromptMsg) {
                    return { validateFlag: true, unsubscribePromptMsg: unsubscribePromptMsg };
                } else {
                    return { validateFlag: true };
                }
            }
        },

        loadBehalfInfo: function (attrData) {
            // 空实现，保持向后兼容
            if (attrData) {
                // 处理代理信息逻辑
            }
        },

        refreshScroll: function () {
            var currHeight = this.$scrollspy.scrollTop();

            this.$scrollspy.scrollTop(0);
            this.$scrollspy.scrollspy('refresh');

            this.$scrollspy.scrollTop(currHeight);
        },

        resize: function (delta) {
            // this.$scrollspy.height(this.$el.parent().height());
            // this.$('#navbar').height(this.$el.parent().height() - 37);
            // delta 参数保留用于向后兼容
            if (delta) {
                // 处理 delta 相关逻辑
            }
        },

        beforeNextStep: function (callBack) {
            this.$orderFeeInfoPreviewView.validateAgreementETP(callBack);
        }
    });
});
