<div class="row oe-order-preview">
    <div class="col-md-12 oe-pre-main">
        <div id="scrollspy" class="scrollspy">
            <div class="panel-body">
                <div id="order-basic-info-preview" class="article-title">{{ORDER_ENTRY_CUSTOMER_BASIC_INFO}}</div>
                <!--客户订单详情表格-->
                <div class='js-cust-order-basic-info'></div>

                <!-- 附件 -->
                <div id="order-attach-preview" class="panel mt12 js-cust-order-attachment-detail" style="margin-bottom: 8px;">
                    <div>
                        <div class="js-cust-order-attachment"></div>
                    </div>
                </div>

                <!-- 安装地址 -->
                <div id ="install-order-preview" class="js-install-order-container">
                    <div class="article-title js-installInfo-title">{{ORDER_ENTRY_INSTALL_INFO}}</div>
                </div>

                 <!-- 投递地址预览 -->
                 <div id="order-delivery-preview" class="js-deliveryInfoPreviewDiv">
                 </div>

                 <!-- 混合销售模式下:Promotion信息预览 -->
                 <div id="order-promotion-preview" class="js-promoPreviewDiv">
                 </div>

                <!-- 优惠券预览 -->
                <div id="order-coupon-info-preview" class="js-couponInfoPreviewDiv">
                 </div>

                <!-- 赠送Offer预览 -->
                <div id="order-bonus-info-preview" class="js-bonusInfoPreviewDiv">
                </div>

                <!-- 费用预览 -->
                <div id="order-fee-info-preview" class="panel mt12 js-order-fee-info-preview">
                    <div class="panel-heading no-border">
                        <span class="panel-title oe-panel-title " style="margin-left: -10px !important;">{{FEE_INFORMATION_PREVIEW}}</span>
                    </div>
                    <div>
                        <div class="js-order-fee-info-preview-detail"></div>
                    </div>
                </div>

                <!--订户定订单列表-->
                <div id="service-list" class="js-order-item-container">
                    <hr class="hr-lg">
                    <div class="panel-title">{{ORDER_ENTRY_ORDER_ITEM_LIST}}</div>
                </div>
            </div>
            <!--div class="js-order-detail">
                {{#each navOrderItemList}}
                    <div id="{{id}}" class="panel js-order-item-detail">
                        <div class="panel-heading no-border" style="padding-bottom: 4px;">
                            <span class="panel-title oe-panel-title " style="margin-left: -10px;">{{name}}</span>
                        </div>
                        <div>
                            <div class="js-order-item-detail-{{id}}"></div>
                        </div>
                    </div>
                {{/each}}
            </div -->
        </div>
    </div>

    <!-- 右侧导航-->
    <div class="col-md-2 oe-pre-anchor">
        <div class="panel col-md-12" style="background-color: rgba(255,255,255,0)">
            <div class="panel-body" style="padding-left: 0;padding-right: 0">
                <nav id="navbar" class="bs-docs-sidebar" role="navigation" style="padding-left: 10px;">
                    <ul class="nav bs-docs-sidenav" style="border-left:solid 1px #e0e0e0;">
                        <li>
                            <span></span>
                            <a href="#order-basic-info-preview" style="margin-top: 0;">
                                {{ORDER_ENTRY_CUSTOMER_BASIC_INFO}}
                            </a>
                        </li>
                        <li class="li-order-attach-preview">
                            <span></span>
                            <a href="#order-attach-preview">
                                {{ATTACHMENTS}}
                            </a>
                        </li>
                       <li class="li-delivery-install-preview">
                               <span></span>
                               <a href="#install-order-preview">
                                 {{ORDER_ENTRY_INSTALL_INFO}}
                              </a>
                        </li>
                        <li class="li-delivery-info-preview">
                            <span></span>
                            <a href="#order-delivery-preview">
                                {{DELIVERY_INFORMATION}}
                            </a>
                        </li>
                        <li class="li-promo-info-preview">
                            <span></span>
                            <a href="#order-promotion-preview">
                                Promotion Information
                            </a>
                        </li>
                        <li class="li-coupon-info-preview">
                            <span></span>
                            <a href="#order-coupon-info-preview">
                                {{COUPONS_INFORMATION}}
                            </a>
                        </li>
                        <li class="li-bonus-info-preview">
                            <span></span>
                            <a href="#order-bonus-info-preview">
                                {{BONUSES_INFORMATION}}
                            </a>
                        </li>
                        <li>
                            <span></span>
                            <a href="#order-fee-info-preview">
                                {{FEE_INFORMATION_PREVIEW}}
                            </a>
                        </li>

                        <li class="js-order-item-nav">
                            <span></span>
                            <a href="#service-list">{{ORDER_ENTRY_ORDER_ITEM_LIST}}</a>
                             {{#recursive itemList}}
                                  <li>
                                       <span class = 'slide-border'></span>
                                       {{#if itemList}}
                                       <span class="iconfont icon-caret-down js-slide-toggle js-slide-toggle-order" style='float:left;'></span>
                                       {{/if}}
                                       <a href="#{{id}}">{{name}}</a>
                                           {{#if itemList}}
                                               <div class="list-box" style="border:none;padding: 0;">
                                                   <ul class="nav">
                                                   {{{recursive itemList}}}
                                                   </ul>
                                               </div>
                                           {{/if}}
                                  </li>
                             {{/recursive}}

                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

</div>
<style>
    .oe-order-preview {
        background: #fff;
        margin: 0;
        padding-bottom: 54px;
        height: calc(100vh - 130px);
        overflow: auto;
        position: relative;
    }

    .oe-order-preview .oe-pre-main {
        padding-right: 14%;
    }

    .oe-order-preview .oe-pre-anchor {
        position: fixed;
        right: 15px;
        width: 13%;
    }

    .oe-order-preview .js-cust-order-detail-form .form-horizontal input,
    .oe-order-preview .js-cust-order-detail-form .form-horizontal textarea {
        border: none !important;
        color: #646464 !important;
        background: none !important;
    }

    .oe-order-preview .js-deliveryInfoDiv .form-horizontal input,
    .oe-order-preview .js-deliveryInfoDiv .form-horizontal textarea {
        border: none !important;
        color: #646464 !important;
        background: none !important;
    }

    .oe-order-preview .js-order-items-tabs .form-horizontal input,
    .oe-order-preview .js-order-items-tabs .form-horizontal textarea {
        border: none !important;
        color: #646464 !important;
        background: none !important;
    }

    .bs-docs-sidenav {
        position: relative;
        margin-top:12px;
    }

    .bs-docs-sidenav .pto-arr-arrowr-ight {
        opacity: 0;
    }

    .bs-docs-sidenav li > a {
        padding-left: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

    }

    .nav > li {
        display: block;
        padding-left: 16px;
    }

    span.oe-selected {
        position: absolute;
        display: inline-block;
        width: 1px;
        height: 18px;
        border: 1px solid #4477EE;
        left:-1px;
    }
    .bs-docs-sidebar .nav > li span.oe-selected + a{
        color:#4477ee;
    }
    .bs-docs-sidebar .nav > li:first-child > a,
    .bs-docs-sidebar .nav > li:last-child > a {
        margin-top: 0;
    }
    .bs-docs-sidebar .nav > li {
        position: inherit;
        border-radius: 0;
    }

    .icon-rotate-270 {
        transform: rotate(270deg);
        -webkit-transform: rotate(270deg);
    }
</style>
<!-- 开发库和过程库代码不一致，过程库同一个元素oe-order-preview被拷贝了两次，导致订单预览界面异常 -->
<!-- 为修改过程库代码，加上此注释交到开发库 -->
