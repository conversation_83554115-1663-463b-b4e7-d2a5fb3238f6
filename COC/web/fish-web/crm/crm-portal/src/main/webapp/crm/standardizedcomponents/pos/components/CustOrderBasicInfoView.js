/**
 * @fileoverview 客户订单基本信息组件
 * @description 显示客户订单的基本信息，包括客户名称、客户类型、事件名称、订单属性、倒签日期和备注
 * <AUTHOR>
 * @since 2025-01-01
 * @version 1.0
 *
 * @component CustOrderBasicInfoView
 * @description 客户订单基本信息展示组件，用于订单预览页面显示客户和订单的基本信息
 *
 * @props {Object} custOrder - 客户订单对象（必填）
 * @props {Object} cust - 客户信息对象（必填）
 * @props {Array} genderArray - 性别数组（可选）
 * @props {String} [commonDataId] - 通用数据ID（可选）
 *
 * @events
 * @event dataLoaded - 数据加载完成事件
 * @param {Object} data - 事件数据
 * @param {Object} data.formData - 表单数据
 *
 * @event error - 错误事件
 * @param {Object} data - 事件数据
 * @param {String} data.message - 错误信息
 * @param {Error} data.error - 错误对象
 *
 * @example
 * // 使用示例
 * this.requireView({
 *     url: 'crm/standardizedcomponents/pos/components/CustOrderBasicInfoView',
 *     selector: '.js-cust-order-basic-info',
 *     viewOption: {
 *         custOrder: this.custOrder,
 *         cust: this.cust,
 *         genderArray: this.genderArray,
 *         commonDataId: this.commonDataId
 *     },
 *     callback: function($view) {
 *         this.custOrderBasicInfoView = $view;
 *     }.bind(this)
 * });
 */
define([
    'hbs!crm/standardizedcomponents/pos/templates/CustOrderBasicInfoTpl.hbs',
    'i18n!crm/modules/pos/workspace/i18n/OrderEntry',
    'crm/modules/customer/actions/OrderAction',
    'crm/modules/common/util/AttrForm',
    'crm/modules/common/constant/CustTypeDef',
    'crm/modules/pos/common/utils/AttrUtil',
    'crm/modules/common/util/DateUtils',
    'crm/modules/common/constant/OrderTypeDef'
], function (template, I18N, OrderAction, AttrForm, CustTypeDef, AttrUtil, DateUtils, OrderTypeDef) {
    return portal.BaseView.extend({
        template: template,

        /**
         * 初始化组件
         * @param {Object} options - 初始化参数
         */
        initialize: function (options) {
            // 标准化改造：设置全局数据访问
            this.options = options || {};

            // 参数验证
            if (!this.options.custOrder) {
                throw new Error('CustOrderBasicInfoView: custOrder is required');
            }
            if (!this.options.cust) {
                throw new Error('CustOrderBasicInfoView: cust is required');
            }

            // 设置组件数据
            this.custOrder = this.options.custOrder;
            this.cust = this.options.cust;
            this.genderArray = this.options.genderArray || [];
            this.commonDataId = this.options.commonDataId;

            // 初始化表单数据
            this.formData = {};
            this.behalfFlag = null;
            this.behalfOfName = null;

            // 合并国际化数据
            this.serialize = fish.extend({}, I18N);
        },

        /**
         * 渲染后处理
         */
        afterRender: function () {
            try {
                this.$custOrderDetailForm = this.$('.js-cust-order-detail-form');
                this.initOrderDetail();
                this.triggerStandardEvent('dataLoaded', { formData: this.formData });
            } catch (error) {
                this.triggerStandardEvent('error', {
                    message: '初始化客户订单基本信息失败',
                    error: error
                });
            }
        },

        /**
         * 初始化订单详情
         */
        initOrderDetail: function () {
            // 构建表单数据
            this.formData = {
                custName: this.cust.CUST_NAME,
                eventName: this.custOrder.SUBS_EVENT_NAME,
                comments: this.custOrder.COMMENTS
            };

            // 设置客户类型名称
            switch (this.cust.CUST_TYPE) {
                case CustTypeDef.INDIVIDUAL:
                    this.formData.custTypeName = CustTypeDef.CUST_TYPE_NAME_CONVERTER[CustTypeDef.INDIVIDUAL];
                    break;
                case CustTypeDef.CORPORATE:
                    this.formData.custTypeName = CustTypeDef.CUST_TYPE_NAME_CONVERTER[CustTypeDef.CORPORATE];
                    break;
            }

            // 处理倒签日期
            if (this.custOrder.BACKDATE) {
                this.$('.js-backdateDiv').show();
                const backdate = DateUtils.displayDate(
                    this.custOrder.BACKDATE,
                    fish.config.get('dateParseFormat.datetime'),
                    fish.config.get('dateParseFormat.datetime')
                );
                this.formData.backdate = backdate;
            }

            // 设置默认值为 '-'
            for (var item in this.formData) {
                if (!this.formData[item]) {
                    this.formData[item] = '-';
                }
            }

            // 填充表单数据
            this.$custOrderDetailForm.form('value', this.formData);

            // 处理空值显示
            this.handleEmptyValues();

            // 初始化客户订单属性表单
            this.initCustOrderAttrForm();
        },

        /**
         * 处理空值显示
         */
        handleEmptyValues: function () {
            var inputText = this.$('.js-cust-order-detail-form .input-group input');
            var textArea = this.$('.js-cust-order-detail-form .input-group textarea');

            fish.forEach(inputText, function (data) {
                if (data.value == '') {
                    $(data).val('-');
                }
            });

            fish.forEach(textArea, function (data) {
                if (data.value == '') {
                    $(data).text('-');
                }
            });
        },

        /**
         * 初始化客户订单属性表单
         */
        initCustOrderAttrForm: function () {
            if (!this.custOrder || fish.isEmpty(this.custOrder.CUST_ORDER_ATTR)) {
                return;
            }

            var that = this;
            var attrData = this.custOrder.CUST_ORDER_ATTR;
            var qryParam = {
                orderType: OrderTypeDef.SUBS_ORDER,
                subsEventId: this.custOrder.SUBS_EVENT_ID,
                custOrder: this.custOrder,
                custOrderFlag: 'Y'
            };

            this.loadBehalfInfo(attrData);

            var dataInput = {};
            dataInput.requestParam = qryParam;

            OrderAction.QryOrderTypeProperty(
                dataInput,
                function (orderTypeProperty) {
                    var orderTypeAttrList = orderTypeProperty.orderTypeAttrList || [];
                    var orderTypePropList = []; // 过滤掉订单中不存在的属性

                    fish.forEach(attrData, function (attr) {
                        var orderTypeProp = fish.find(orderTypeAttrList, function (item) {
                            return item.attrId == attr.ATTR_ID;
                        });
                        if (orderTypeProp) {
                            orderTypeProp.editable = 'N';
                            orderTypeProp.nullable = 'Y'; // 不需要展示必填的星号了吧
                            orderTypeProp.attrValue = attr.VALUE;
                            orderTypeProp.inputType = '4'; // text展示，不查属性值列表，减少消耗
                            if (orderTypeProp.attrValueList) {
                                var attrValueData = fish.find(orderTypeProp.attrValueList, function (obj) {
                                    return attr.VALUE == obj.value;
                                });
                                if (attrValueData) {
                                    attr.ATTR_VALUE_MARK = attrValueData.valueMark;
                                    orderTypeProp.defaultValue = attrValueData.valueMark;
                                }
                            }
                            orderTypePropList.push(orderTypeProp);
                        }
                    });

                    if (that.behalfFlag) {
                        var behalfFlag = {
                            attrId: -2,
                            editable: 'N',
                            attrName: 'On Behalf Flag',
                            attrValue: that.behalfFlag,
                            defaultValue: that.behalfFlag
                        };
                        orderTypePropList.push(behalfFlag);
                    }
                    if (that.behalfOfName) {
                        var behalfProp = {
                            attrId: -1,
                            editable: 'N',
                            attrName: 'On Behalf Of',
                            attrValue: that.behalfOfName,
                            defaultValue: that.behalfOfName
                        };
                        orderTypePropList.push(behalfProp);
                    }

                    AttrForm.renderAttrForm('.cust-order-attr-info', orderTypePropList, { col: 3 }, this);
                    attrData = AttrUtil.deepObjToCamel(attrData);
                    AttrForm.setAttrFormData('.cust-order-attr-info', attrData, this);
                }.bind(this)
            );
        },

        /**
         * 加载代理信息
         * @param {Array} attrData - 属性数据
         */
        loadBehalfInfo: function (attrData) {
            // 这里可以根据需要实现代理信息加载逻辑
            // 当前为空实现，可根据业务需要扩展
            if (attrData) {
                // 处理代理信息逻辑
            }
        },

        /**
         * 标准事件触发方法
         * @param {String} eventName - 事件名称
         * @param {Object} businessData - 业务数据
         */
        triggerStandardEvent: function (eventName, businessData) {
            var eventData = {
                type: eventName,
                source: 'CustOrderBasicInfoView',
                timestamp: Date.now(),
                data: businessData || {}
            };

            this.trigger(eventName, eventData);
        },

        /**
         * 获取表单数据
         * @returns {Object} 表单数据
         */
        getFormData: function () {
            return this.formData;
        },

        /**
         * 刷新数据
         */
        refreshData: function () {
            this.initOrderDetail();
        }
    });
});
